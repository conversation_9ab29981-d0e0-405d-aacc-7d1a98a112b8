<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 桌面端性能优化服务
 *
 * 提供各种性能优化策略和工具
 */
class DesktopPerformanceOptimizer
{
    private $config;
    private $metrics;

    public function __construct()
    {
        $this->config = config('performance.desktop', []);
        $this->metrics = [];
    }

    /**
     * 应用启动优化
     */
    public function optimizeStartup()
    {
        $startTime = microtime(true);

        // 1. 预加载关键配置
        $this->preloadConfigurations();

        // 2. 初始化缓存
        $this->initializeCache();

        // 3. 预连接数据库
        $this->preconnectDatabase();

        // 4. 预加载关键资源
        $this->preloadCriticalAssets();

        // 5. 优化内存使用
        $this->optimizeMemoryUsage();

        $this->metrics['startup_time'] = (microtime(true) - $startTime) * 1000;
        
        Log::info('Desktop startup optimized', $this->metrics);
    }

    /**
     * 预加载关键配置
     */
    private function preloadConfigurations()
    {
        $configs = [
            'app',
            'database',
            'cache',
            'session',
            'nativephp',
        ];

        foreach ($configs as $config) {
            config($config);
        }
    }

    /**
     * 初始化缓存系统
     */
    private function initializeCache()
    {
        // 预热关键缓存
        $cacheKeys = [
            'user_permissions',
            'system_settings',
            'menu_structure',
            'project_templates',
        ];

        foreach ($cacheKeys as $key) {
            if (!Cache::has($key)) {
                $this->warmupCache($key);
            }
        }
    }

    /**
     * 缓存预热
     */
    private function warmupCache($key)
    {
        switch ($key) {
            case 'user_permissions':
                Cache::remember('user_permissions', 3600, function () {
                    return DB::table('permissions')->get();
                });
                break;

            case 'system_settings':
                Cache::remember('system_settings', 7200, function () {
                    return DB::table('settings')->pluck('value', 'key');
                });
                break;

            case 'menu_structure':
                Cache::remember('menu_structure', 1800, function () {
                    return $this->buildMenuStructure();
                });
                break;

            case 'project_templates':
                Cache::remember('project_templates', 3600, function () {
                    return DB::table('project_templates')->get();
                });
                break;
        }
    }

    /**
     * 预连接数据库
     */
    private function preconnectDatabase()
    {
        try {
            // 建立数据库连接池
            DB::connection()->getPdo();
            
            // SQLite优化设置
            if (config('database.default') === 'sqlite') {
                DB::statement('PRAGMA journal_mode=WAL');
                DB::statement('PRAGMA synchronous=NORMAL');
                DB::statement('PRAGMA cache_size=10000');
                DB::statement('PRAGMA temp_store=MEMORY');
            }
        } catch (\Exception $e) {
            Log::error('Database preconnection failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 预加载关键资源
     */
    private function preloadCriticalAssets()
    {
        $criticalAssets = [
            'css/app.css',
            'js/app.js',
            'images/logo.png',
            'fonts/main.woff2',
        ];

        foreach ($criticalAssets as $asset) {
            $path = public_path($asset);
            if (file_exists($path)) {
                // 预加载到内存
                file_get_contents($path);
            }
        }
    }

    /**
     * 优化内存使用
     */
    private function optimizeMemoryUsage()
    {
        // 设置内存限制
        ini_set('memory_limit', '512M');

        // 启用OPcache
        if (function_exists('opcache_compile_file')) {
            $this->precompileFiles();
        }

        // 垃圾回收优化
        gc_enable();
        gc_collect_cycles();
    }

    /**
     * 预编译PHP文件
     */
    private function precompileFiles()
    {
        $files = [
            app_path('Http/Controllers/DashboardController.php'),
            app_path('Http/Controllers/ProjectController.php'),
            app_path('Http/Controllers/TaskController.php'),
            app_path('Services/ProjectService.php'),
            app_path('Services/TaskService.php'),
        ];

        foreach ($files as $file) {
            if (file_exists($file)) {
                opcache_compile_file($file);
            }
        }
    }

    /**
     * 数据库查询优化
     */
    public function optimizeQueries()
    {
        // 1. 启用查询缓存
        $this->enableQueryCache();

        // 2. 优化常用查询
        $this->optimizeCommonQueries();

        // 3. 创建必要索引
        $this->createOptimalIndexes();
    }

    /**
     * 启用查询缓存
     */
    private function enableQueryCache()
    {
        // 缓存常用查询结果
        $queries = [
            'active_projects' => 'SELECT * FROM projects WHERE status = "active" ORDER BY updated_at DESC',
            'recent_tasks' => 'SELECT * FROM tasks WHERE updated_at > DATE_SUB(NOW(), INTERVAL 7 DAY)',
            'user_stats' => 'SELECT COUNT(*) as total_tasks, SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed_tasks FROM tasks WHERE user_id = ?',
        ];

        foreach ($queries as $key => $query) {
            Cache::remember("query_{$key}", 300, function () use ($query) {
                return DB::select($query);
            });
        }
    }

    /**
     * 优化常用查询
     */
    private function optimizeCommonQueries()
    {
        // 项目列表查询优化
        DB::statement('
            CREATE VIEW IF NOT EXISTS project_summary AS
            SELECT 
                p.id,
                p.name,
                p.status,
                p.updated_at,
                COUNT(t.id) as task_count,
                SUM(CASE WHEN t.status = "completed" THEN 1 ELSE 0 END) as completed_tasks
            FROM projects p
            LEFT JOIN tasks t ON p.id = t.project_id
            GROUP BY p.id, p.name, p.status, p.updated_at
        ');

        // 用户仪表板查询优化
        DB::statement('
            CREATE VIEW IF NOT EXISTS user_dashboard AS
            SELECT 
                u.id as user_id,
                COUNT(DISTINCT p.id) as project_count,
                COUNT(t.id) as total_tasks,
                SUM(CASE WHEN t.status = "completed" THEN 1 ELSE 0 END) as completed_tasks,
                SUM(CASE WHEN t.due_date < NOW() AND t.status != "completed" THEN 1 ELSE 0 END) as overdue_tasks
            FROM users u
            LEFT JOIN project_members pm ON u.id = pm.user_id
            LEFT JOIN projects p ON pm.project_id = p.id
            LEFT JOIN tasks t ON p.id = t.project_id AND t.assigned_to = u.id
            GROUP BY u.id
        ');
    }

    /**
     * 创建最优索引
     */
    private function createOptimalIndexes()
    {
        $indexes = [
            'tasks' => [
                'idx_tasks_status_updated' => ['status', 'updated_at'],
                'idx_tasks_assigned_due' => ['assigned_to', 'due_date'],
                'idx_tasks_project_status' => ['project_id', 'status'],
            ],
            'projects' => [
                'idx_projects_status_updated' => ['status', 'updated_at'],
                'idx_projects_owner_status' => ['owner_id', 'status'],
            ],
            'project_members' => [
                'idx_pm_user_project' => ['user_id', 'project_id'],
                'idx_pm_project_role' => ['project_id', 'role'],
            ],
        ];

        foreach ($indexes as $table => $tableIndexes) {
            foreach ($tableIndexes as $indexName => $columns) {
                $columnList = implode(', ', $columns);
                try {
                    DB::statement("CREATE INDEX IF NOT EXISTS {$indexName} ON {$table} ({$columnList})");
                } catch (\Exception $e) {
                    Log::warning("Failed to create index {$indexName}", ['error' => $e->getMessage()]);
                }
            }
        }
    }

    /**
     * 前端资源优化
     */
    public function optimizeFrontendAssets()
    {
        // 1. CSS优化
        $this->optimizeCSS();

        // 2. JavaScript优化
        $this->optimizeJavaScript();

        // 3. 图片优化
        $this->optimizeImages();

        // 4. 字体优化
        $this->optimizeFonts();
    }

    /**
     * CSS优化
     */
    private function optimizeCSS()
    {
        $cssFiles = [
            'resources/css/app.css',
            'resources/css/desktop.css',
        ];

        foreach ($cssFiles as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                
                // 移除注释和空白
                $content = preg_replace('/\/\*.*?\*\//s', '', $content);
                $content = preg_replace('/\s+/', ' ', $content);
                
                // 压缩CSS
                $minifiedFile = str_replace('.css', '.min.css', $file);
                file_put_contents($minifiedFile, $content);
            }
        }
    }

    /**
     * JavaScript优化
     */
    private function optimizeJavaScript()
    {
        $jsFiles = [
            'resources/js/app.js',
            'resources/js/desktop.js',
        ];

        foreach ($jsFiles as $file) {
            if (file_exists($file)) {
                // 使用Terser或类似工具压缩JavaScript
                $minifiedFile = str_replace('.js', '.min.js', $file);
                exec("npx terser {$file} -o {$minifiedFile} --compress --mangle", $output, $returnCode);
                
                if ($returnCode !== 0) {
                    Log::warning("Failed to minify {$file}");
                }
            }
        }
    }

    /**
     * 图片优化
     */
    private function optimizeImages()
    {
        $imageDir = public_path('images');
        $images = glob($imageDir . '/*.{jpg,jpeg,png,gif}', GLOB_BRACE);

        foreach ($images as $image) {
            $info = getimagesize($image);
            if ($info === false) continue;

            $extension = strtolower(pathinfo($image, PATHINFO_EXTENSION));
            
            switch ($extension) {
                case 'jpg':
                case 'jpeg':
                    $this->optimizeJPEG($image);
                    break;
                case 'png':
                    $this->optimizePNG($image);
                    break;
            }
        }
    }

    /**
     * JPEG优化
     */
    private function optimizeJPEG($imagePath)
    {
        $image = imagecreatefromjpeg($imagePath);
        if ($image) {
            imagejpeg($image, $imagePath, 85); // 85%质量
            imagedestroy($image);
        }
    }

    /**
     * PNG优化
     */
    private function optimizePNG($imagePath)
    {
        $image = imagecreatefrompng($imagePath);
        if ($image) {
            imagepng($image, $imagePath, 6); // 压缩级别6
            imagedestroy($image);
        }
    }

    /**
     * 字体优化
     */
    private function optimizeFonts()
    {
        // 预加载关键字体
        $fonts = [
            'fonts/inter-regular.woff2',
            'fonts/inter-medium.woff2',
            'fonts/inter-bold.woff2',
        ];

        foreach ($fonts as $font) {
            $fontPath = public_path($font);
            if (file_exists($fontPath)) {
                // 预加载字体到内存
                file_get_contents($fontPath);
            }
        }
    }

    /**
     * 实时性能监控
     */
    public function startPerformanceMonitoring()
    {
        // 监控内存使用
        $this->monitorMemoryUsage();

        // 监控CPU使用
        $this->monitorCPUUsage();

        // 监控数据库性能
        $this->monitorDatabasePerformance();
    }

    /**
     * 监控内存使用
     */
    private function monitorMemoryUsage()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);

        if ($memoryUsage > 400 * 1024 * 1024) { // 400MB
            Log::warning('High memory usage detected', [
                'current_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
                'peak_usage_mb' => round($memoryPeak / 1024 / 1024, 2),
            ]);
        }
    }

    /**
     * 监控CPU使用
     */
    private function monitorCPUUsage()
    {
        $load = sys_getloadavg();
        if ($load[0] > 2.0) { // 负载过高
            Log::warning('High CPU load detected', [
                'load_1min' => $load[0],
                'load_5min' => $load[1],
                'load_15min' => $load[2],
            ]);
        }
    }

    /**
     * 监控数据库性能
     */
    private function monitorDatabasePerformance()
    {
        $slowQueries = DB::select('
            SELECT * FROM information_schema.processlist 
            WHERE time > 5 AND command != "Sleep"
        ');

        if (!empty($slowQueries)) {
            Log::warning('Slow database queries detected', [
                'slow_query_count' => count($slowQueries),
                'queries' => $slowQueries,
            ]);
        }
    }

    /**
     * 获取性能指标
     */
    public function getPerformanceMetrics()
    {
        return [
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'memory_peak_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'cpu_load' => sys_getloadavg(),
            'cache_hit_rate' => $this->getCacheHitRate(),
            'database_connections' => $this->getDatabaseConnections(),
            'startup_time_ms' => $this->metrics['startup_time'] ?? 0,
        ];
    }

    /**
     * 获取缓存命中率
     */
    private function getCacheHitRate()
    {
        // 实现缓存命中率计算逻辑
        return 0.85; // 示例值
    }

    /**
     * 获取数据库连接数
     */
    private function getDatabaseConnections()
    {
        try {
            $result = DB::select('SELECT COUNT(*) as count FROM information_schema.processlist');
            return $result[0]->count ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 构建菜单结构
     */
    private function buildMenuStructure()
    {
        // 实现菜单结构构建逻辑
        return [
            'dashboard' => ['name' => '仪表板', 'icon' => 'dashboard'],
            'projects' => ['name' => '项目', 'icon' => 'folder'],
            'tasks' => ['name' => '任务', 'icon' => 'tasks'],
            'calendar' => ['name' => '日历', 'icon' => 'calendar'],
            'reports' => ['name' => '报告', 'icon' => 'chart'],
        ];
    }
}
