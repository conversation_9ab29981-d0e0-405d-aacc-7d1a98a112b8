# 📦 Leantime NativePHP桌面端打包方案

## 📋 文档概述

本文档为Leantime项目转换为NativePHP桌面应用的完整打包方案，涵盖从开发环境到生产部署的全流程指导。

---

## 🎯 打包目标与策略

### 目标平台支持
- **Windows** (Windows 10/11, x64)
- **macOS** (macOS 11+, Intel & Apple Silicon)
- **Linux** (Ubuntu 20.04+, CentOS 8+)

### 打包策略概览
- **单一代码库** - 一套代码支持多平台
- **自动化构建** - CI/CD集成自动打包
- **增量更新** - 支持应用自动更新
- **数字签名** - 确保应用安全性

---

## 🏗️ 打包架构设计

### 应用架构层次

```
┌─────────────────────────────────────┐
│           用户界面层                  │
│    (Electron WebView + HTML/CSS)    │
├─────────────────────────────────────┤
│           业务逻辑层                  │
│      (Laravel + PHP Runtime)        │
├─────────────────────────────────────┤
│           数据存储层                  │
│         (SQLite Database)           │
├─────────────────────────────────────┤
│           系统集成层                  │
│    (Native APIs + File System)      │
└─────────────────────────────────────┘
```

### 打包组件构成

| 组件类型 | 组件名称 | 大小估算 | 作用描述 |
|----------|----------|----------|----------|
| **核心运行时** | PHP Runtime | ~50MB | 嵌入式PHP执行环境 |
| **应用框架** | Laravel Framework | ~30MB | Web应用框架 |
| **用户界面** | Electron Shell | ~120MB | 桌面应用容器 |
| **业务代码** | Leantime Source | ~25MB | 项目管理业务逻辑 |
| **数据库** | SQLite Engine | ~2MB | 本地数据存储 |
| **资源文件** | Assets & Media | ~15MB | 图片、字体、样式 |
| **总计** | **完整应用包** | **~240MB** | 跨平台桌面应用 |

---

## 🔄 打包流程设计

### 完整打包流程图

```mermaid
graph TD
    A[开发环境准备] --> B[代码质量检查]
    B --> C[依赖管理优化]
    C --> D[资源文件处理]
    D --> E[数据库迁移]
    E --> F[配置文件生成]
    F --> G[多平台构建]
    G --> H[应用签名]
    H --> I[安装包生成]
    I --> J[质量测试]
    J --> K[发布部署]
    
    G --> G1[Windows构建]
    G --> G2[macOS构建]
    G --> G3[Linux构建]
    
    H --> H1[Windows代码签名]
    H --> H2[macOS公证]
    H --> H3[Linux GPG签名]
    
    I --> I1[Windows MSI/EXE]
    I --> I2[macOS DMG/PKG]
    I --> I3[Linux DEB/RPM/AppImage]
```

### 关键阶段详解

#### 阶段1：环境准备 (1-2天)
- **开发工具配置** - NativePHP、Electron、构建工具
- **依赖项检查** - PHP版本、Node.js、系统库
- **证书准备** - 代码签名证书申请和配置

#### 阶段2：代码优化 (3-5天)
- **性能优化** - 代码压缩、资源优化
- **兼容性处理** - 跨平台API适配
- **安全加固** - 敏感信息保护

#### 阶段3：构建打包 (2-3天)
- **自动化构建** - CI/CD流水线配置
- **多平台编译** - 并行构建不同平台版本
- **质量验证** - 自动化测试和验收

#### 阶段4：发布部署 (1-2天)
- **数字签名** - 应用可信度认证
- **分发渠道** - 官网、应用商店、企业内部
- **更新机制** - 自动更新服务配置

---

## 🛠️ 构建环境配置

### 开发环境要求

| 环境类型 | 最低配置 | 推荐配置 | 备注 |
|----------|----------|----------|------|
| **操作系统** | Windows 10/macOS 11/Ubuntu 20.04 | 最新稳定版 | 支持跨平台开发 |
| **内存** | 8GB RAM | 16GB+ RAM | 构建过程内存密集 |
| **存储** | 50GB 可用空间 | 100GB+ SSD | 多平台构建需要空间 |
| **网络** | 稳定互联网连接 | 高速宽带 | 下载依赖和上传构建产物 |

### 必需软件工具

```
开发工具链
├── PHP 8.2+
├── Composer 2.0+
├── Node.js 18.0+
├── NPM/Yarn
├── Git 2.30+
└── Docker (可选)

构建工具
├── Electron Builder
├── NativePHP CLI
├── Webpack/Vite
└── 平台特定工具
    ├── Windows: Visual Studio Build Tools
    ├── macOS: Xcode Command Line Tools
    └── Linux: Build Essential
```

---

## 📊 打包配置策略

### 配置文件层次结构

```
配置管理
├── 基础配置 (base.json)
│   ├── 应用基本信息
│   ├── 通用构建选项
│   └── 共享资源定义
├── 平台配置
│   ├── Windows (windows.json)
│   ├── macOS (macos.json)
│   └── Linux (linux.json)
├── 环境配置
│   ├── 开发环境 (development.json)
│   ├── 测试环境 (staging.json)
│   └── 生产环境 (production.json)
└── 自定义配置
    ├── 企业定制 (enterprise.json)
    └── 特殊需求 (custom.json)
```

### 关键配置参数

| 配置类别 | 参数名称 | 作用说明 | 配置建议 |
|----------|----------|----------|----------|
| **应用信息** | appId | 应用唯一标识 | com.leantime.desktop |
| **应用信息** | productName | 应用显示名称 | Leantime Desktop |
| **应用信息** | version | 应用版本号 | 语义化版本 (1.0.0) |
| **构建选项** | compression | 压缩算法 | maximum (最大压缩) |
| **构建选项** | asar | 代码打包 | true (启用保护) |
| **更新机制** | publish | 发布配置 | 自动更新服务器 |
| **安全选项** | codeSign | 代码签名 | 生产环境必需 |

---

## 🔐 安全与签名策略

### 代码签名重要性

```
代码签名价值链
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   开发者身份     │───▶│   应用完整性     │───▶│   用户信任度     │
│   Developer ID   │    │   Code Integrity │    │   User Trust    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
   防止身份伪造              防止代码篡改              减少安全警告
```

### 平台签名要求

#### Windows平台
- **证书类型**: EV代码签名证书 (推荐) 或标准代码签名证书
- **签名工具**: SignTool.exe
- **时间戳**: 必须添加时间戳服务器
- **SmartScreen**: EV证书可立即获得信誉

#### macOS平台
- **开发者账号**: Apple Developer Program ($99/年)
- **证书类型**: Developer ID Application
- **公证流程**: 必须通过Apple公证服务
- **Gatekeeper**: 确保系统安全检查通过

#### Linux平台
- **GPG签名**: 使用GPG密钥签名软件包
- **仓库分发**: 通过可信软件仓库分发
- **校验和**: 提供SHA256校验和文件

---

## 📈 性能优化策略

### 应用启动优化

```
启动性能优化路径
┌─────────────────┐
│   冷启动 (3-5s)  │
└─────────┬───────┘
          │ 优化后
          ▼
┌─────────────────┐
│   热启动 (1-2s)  │
└─────────┬───────┘
          │ 进一步优化
          ▼
┌─────────────────┐
│  即时启动 (<1s)  │
└─────────────────┘
```

### 优化技术手段

| 优化类别 | 技术手段 | 性能提升 | 实施难度 |
|----------|----------|----------|----------|
| **代码优化** | PHP OPcache | 30-50% | 低 |
| **资源优化** | 静态资源压缩 | 20-30% | 低 |
| **缓存策略** | 应用级缓存 | 40-60% | 中 |
| **数据库优化** | SQLite调优 | 25-40% | 中 |
| **预加载** | 关键组件预加载 | 35-50% | 高 |
| **懒加载** | 按需加载模块 | 20-35% | 中 |

### 内存使用优化

```
内存使用分布
┌─────────────────────────────────────┐
│ PHP Runtime     │████████████ 40%   │
│ Electron Shell  │██████████ 35%     │
│ Application Code│████ 15%           │
│ Database Cache  │██ 7%              │
│ Other Resources │█ 3%               │
└─────────────────────────────────────┘
总内存使用: ~400MB (优化后)
```

---

## 🚀 部署与分发策略

### 分发渠道矩阵

| 分发渠道 | 适用场景 | 优势 | 劣势 | 推荐度 |
|----------|----------|------|------|--------|
| **官方网站** | 通用下载 | 完全控制、无审核 | 需要自建基础设施 | ⭐⭐⭐⭐⭐ |
| **Microsoft Store** | Windows用户 | 官方认证、自动更新 | 审核严格、分成30% | ⭐⭐⭐⭐ |
| **Mac App Store** | macOS用户 | 系统集成、安全性高 | 审核严格、功能限制 | ⭐⭐⭐ |
| **Snap Store** | Linux用户 | 跨发行版、自动更新 | 性能开销、兼容性 | ⭐⭐⭐ |
| **企业内部** | 企业客户 | 定制化、安全可控 | 维护成本高 | ⭐⭐⭐⭐ |

### 更新机制设计

```
自动更新流程
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   检查更新       │───▶│   下载更新       │───▶│   安装更新       │
│   Check Update  │    │   Download      │    │   Install       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         ▼                        ▼                        ▼
   版本比较逻辑              增量下载优化              静默安装重启
```

### 版本管理策略

#### 语义化版本控制
- **主版本号** (Major): 不兼容的API修改
- **次版本号** (Minor): 向下兼容的功能性新增
- **修订号** (Patch): 向下兼容的问题修正

#### 发布周期规划
- **稳定版** (Stable): 每季度发布，经过充分测试
- **预览版** (Preview): 每月发布，包含新功能预览
- **热修复** (Hotfix): 按需发布，修复严重问题

---

## 📊 质量保证体系

### 测试金字塔

```
测试层次结构
        ┌─────────────────┐
        │   E2E测试 (10%)  │  ← 用户场景测试
        └─────────────────┘
      ┌─────────────────────┐
      │  集成测试 (20%)      │    ← 组件协作测试
      └─────────────────────┘
    ┌─────────────────────────┐
    │    单元测试 (70%)        │      ← 功能逻辑测试
    └─────────────────────────┘
```

### 质量检查清单

#### 功能性测试
- [ ] 核心业务功能完整性
- [ ] 用户界面响应性
- [ ] 数据持久化正确性
- [ ] 跨平台兼容性

#### 性能测试
- [ ] 应用启动时间 < 3秒
- [ ] 内存使用 < 500MB
- [ ] CPU使用率合理
- [ ] 磁盘I/O优化

#### 安全测试
- [ ] 代码签名验证
- [ ] 数据加密保护
- [ ] 权限控制检查
- [ ] 漏洞扫描通过

#### 用户体验测试
- [ ] 界面美观一致
- [ ] 操作流程顺畅
- [ ] 错误处理友好
- [ ] 帮助文档完整

---

## 📋 项目里程碑

### 实施时间表

```
项目时间线 (总计: 8-12周)
┌─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┬─────┐
│ W1  │ W2  │ W3  │ W4  │ W5  │ W6  │ W7  │ W8  │ W9  │ W10 │ W11 │ W12 │
├─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┼─────┤
│ 环境 │ 代码 │ 数据 │ 配置 │ 构建 │ 测试 │ 优化 │ 签名 │ 分发 │ 验收 │ 发布 │ 维护 │
│ 准备 │ 迁移 │ 迁移 │ 调试 │ 打包 │ 验证 │ 性能 │ 认证 │ 部署 │ 测试 │ 上线 │ 支持 │
└─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┴─────┘
```

### 关键里程碑

| 里程碑 | 完成时间 | 交付物 | 验收标准 |
|--------|----------|--------|----------|
| **M1: 环境就绪** | 第2周 | 开发环境、工具链 | 构建流程可执行 |
| **M2: 功能迁移** | 第4周 | 核心功能移植 | 主要业务流程正常 |
| **M3: 打包成功** | 第6周 | 可执行应用包 | 三平台构建成功 |
| **M4: 测试通过** | 第8周 | 测试报告 | 质量标准达标 |
| **M5: 正式发布** | 第10周 | 生产版本 | 用户可正常使用 |

---

## 🎯 成功关键因素

### 技术成功要素
- **架构设计合理** - 模块化、可扩展的系统架构
- **性能优化到位** - 满足用户体验期望的响应速度
- **兼容性良好** - 跨平台一致的用户体验
- **安全性可靠** - 完善的安全防护机制

### 项目管理要素
- **团队技能匹配** - 具备相关技术栈经验的开发团队
- **时间规划合理** - 充分的开发和测试时间预留
- **质量控制严格** - 完善的测试和验收流程
- **风险管控有效** - 及时识别和应对潜在风险

### 用户接受要素
- **功能完整性** - 满足用户核心需求的功能覆盖
- **易用性良好** - 符合用户习惯的交互设计
- **稳定性可靠** - 低故障率的稳定运行
- **支持服务完善** - 及时响应的技术支持

---

## 📞 支持与维护

### 技术支持体系
- **文档支持** - 完整的用户手册和开发文档
- **社区支持** - 活跃的用户社区和开发者论坛
- **专业支持** - 企业级技术支持服务
- **培训服务** - 用户培训和开发者培训

### 持续维护计划
- **定期更新** - 功能增强和安全补丁
- **性能监控** - 应用性能和用户体验监控
- **用户反馈** - 收集和处理用户意见建议
- **技术演进** - 跟进技术发展和平台更新

---

*本文档将随着项目进展持续更新和完善*
